'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { HiOutlineArrowNarrowRight } from 'react-icons/hi';
import Image from 'next/image';
import LandingpageComponentCarousel from './landingpage/LandingpageComponentCarousel';

export default function LandingpageCarousel() {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [autoScrollPaused, setAutoScrollPaused] = useState(false);
  const [isVisible, setIsVisible] = useState({});
  const sectionRefs = useRef({});
  const [heroImages, setHeroImages] = useState([]);

  // console.log('LandingpageCarousell:', currentSlide);

  useEffect(() => {
    const getHeroImages = async () => {
      try {
        const response = await fetch('/api/hero-images');
        const result = await response.json();

        console.log('getHeroImages:', result);
        
        if (result.success) {
          const carouselImages = result.data.map(item => ({
            url: item.url,
            name: item.name,
            id: item._id
          }));
          setHeroImages(carouselImages);
        }
      } catch (error) {
        console.error('Error fetching hero images:', error);
      }
    };

    getHeroImages();
  }, []);

  // Hero carousel images with absolute URLs
  // const heroImages = [
  //   '/hero/0005.jpg',
  //   '/hero/View_1-Lawn-Option-1.jpg',
  //   '/hero/View_3-Pool-Option-1.jpg',
  // ];

  // Portfolio projects
  const portfolioProjects = [
    {
      image: '/hero/0003_Opt1.jpg',
      title: 'Modern Residential',
      category: 'Exterior Visualization'
    },
    {
      image: '/hero/0003_Opt2.jpg',
      title: 'Contemporary Villa',
      category: 'Exterior Visualization'
    },
    {
      image: '/hero/0005.jpg',
      title: 'Urban Apartment',
      category: 'Interior Visualization'
    },
    {
      image: '/hero/0006.jpg',
      title: 'Luxury Residence',
      category: 'Exterior Visualization'
    }
  ];

  // Services data
  const services = [
    {
      title: "Architectural Visualization",
      description: "Transform blueprints into photorealistic 3D renderings that bring your designs to life before construction begins."
    },
    {
      title: "Interior Visualization",
      description: "Explore interior spaces with detailed visualizations that showcase materials, lighting, and spatial relationships."
    },
    {
      title: "Design Development",
      description: "Refine your architectural concepts with iterative visualizations that help clients understand design decisions."
    }
  ];

  // Auto-scroll for hero carousel
  useEffect(() => {
    if (autoScrollPaused) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % heroImages.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [autoScrollPaused, heroImages.length]);

  // Handle manual navigation
  const goToSlide = (index) => {
    setCurrentSlide(index);
    setAutoScrollPaused(true);

    // Resume auto-scroll after 15 seconds
    setTimeout(() => {
      setAutoScrollPaused(false);
    }, 15000);
  };

  // Intersection observer for animations
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1
    };

    const observerCallback = (entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setIsVisible(prev => ({ ...prev, [entry.target.id]: true }));
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback, observerOptions);

    // Observe all section refs
    Object.keys(sectionRefs.current).forEach(key => {
      if (sectionRefs.current[key]) {
        observer.observe(sectionRefs.current[key]);
      }
    });

    return () => {
      Object.keys(sectionRefs.current).forEach(key => {
        if (sectionRefs.current[key]) {
          observer.unobserve(sectionRefs.current[key]);
        }
      });
    };
  }, []);

  // Register a section ref
  const registerSectionRef = (id, el) => {
    if (el && !sectionRefs.current[id]) {
      sectionRefs.current[id] = el;
    }
  };

  return (
    <div className="min-h-screen bg-white relative overflow-y-auto">
      {/* Hero Section */}
      <section className="relative w-full h-screen">
        {/* Hero Carousel */}
        <div className='flex z-10 absolute w-full h-full'>
          {heroImages.map((image, index) => (
            <div 
              key={index}
              className={`absolute w-full h-full overflow-hidden transition-opacity duration-1000 ${currentSlide === index ? 'opacity-100' : 'opacity-0'}`}
               style={{
                backgroundImage: `url(${image.url})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center'
              }}
              aria-label={`Architectural visualization ${index + 1}`}
            >
              div
              <Image
                key={index}
                src={image.url}
                alt={`Architectural visualization ${index + 1}`}
                fill
                priority={index === 0}
                sizes="100vw"
                quality={90}
                className="object-cover brightness-80"
              />
            </div>
          ))}
        </div>

        {/* Hero Content */}
        <div className="relative z-10 h-full flex flex-col items-center justify-center text-center px-4 md:px-8">
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-4xl md:text-6xl uppercase font-thin text-white mb-6 tracking-wide"
          >
            <span className="block mb-2">welcome to</span>
            <span className="font-light">elephant island</span>
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-lg md:text-3xl text-white mb-10 uppercase max-w-96 font-thin tracking-wide"
          >
            your home away from home experience 
          </motion.p>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-lg md:text-3xl text-white mb-10 uppercase max-w-2xl font-thin tracking-wide"
          >
            <LandingpageComponentCarousel/>
          </motion.div>
        </div>

        {/* Carousel Navigation */}
        <div className="CarouselWrapp absolute bottom-20 left-0 right-0 z-10 flex justify-center space-x-3">
          {heroImages.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-2 h-2 rounded-full cursor-pointer transition-colors ease-linear duration-300 ${
                currentSlide === index ? 'bg-white w-10 ' : 'bg-white/40 w-2'
              }`}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      </section>
    </div>
  );
}
